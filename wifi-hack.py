#!/usr/bin/env python3
"""
WiFi Password Retriever
This script retrieves saved WiFi passwords on Windows systems.
"""

import subprocess
import re
import sys
import os

def get_wifi_profiles():
    """Get all WiFi profiles saved on the system."""
    try:
        # Run netsh command to get WiFi profiles
        result = subprocess.run(
            ['netsh', 'wlan', 'show', 'profiles'],
            capture_output=True,
            text=True,
            check=True
        )

        # Extract profile names using regex
        profiles = []
        for line in result.stdout.split('\n'):
            if 'All User Profile' in line:
                # Extract profile name
                profile_match = re.search(r':\s*(.+)', line)
                if profile_match:
                    profile_name = profile_match.group(1).strip()
                    profiles.append(profile_name)

        return profiles

    except subprocess.CalledProcessError as e:
        print(f"Error getting WiFi profiles: {e}")
        return []
    except Exception as e:
        print(f"Unexpected error: {e}")
        return []

def get_wifi_password(profile_name):
    """Get the password for a specific WiFi profile."""
    try:
        # Run netsh command to get profile details with key
        result = subprocess.run(
            ['netsh', 'wlan', 'show', 'profile', profile_name, 'key=clear'],
            capture_output=True,
            text=True,
            check=True
        )

        # Look for the key content (password)
        for line in result.stdout.split('\n'):
            if 'Key Content' in line:
                password_match = re.search(r':\s*(.+)', line)
                if password_match:
                    return password_match.group(1).strip()

        return "No password found or open network"

    except subprocess.CalledProcessError:
        return "Access denied or profile not found"
    except Exception as e:
        return f"Error: {e}"

def main():
    """Main function to retrieve and display WiFi passwords."""
    print("WiFi Password Retriever")
    print("=" * 50)

    # Check if running on Windows
    if os.name != 'nt':
        print("This script is designed for Windows systems only.")
        sys.exit(1)

    # Get all WiFi profiles
    print("Retrieving WiFi profiles...")
    profiles = get_wifi_profiles()

    if not profiles:
        print("No WiFi profiles found.")
        return

    print(f"Found {len(profiles)} WiFi profile(s):\n")

    # Get password for each profile
    wifi_data = []
    for profile in profiles:
        password = get_wifi_password(profile)
        wifi_data.append((profile, password))

    # Display results
    print("WiFi Networks and Passwords:")
    print("-" * 50)

    for profile, password in wifi_data:
        print(f"Network: {profile}")
        print(f"Password: {password}")
        print("-" * 30)

    # Option to save to file
    save_choice = input("\nDo you want to save this information to a file? (y/n): ").lower()
    if save_choice == 'y':
        save_to_file(wifi_data)

def save_to_file(wifi_data):
    """Save WiFi data to a text file."""
    try:
        filename = "wifi_passwords.txt"
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("WiFi Networks and Passwords\n")
            f.write("=" * 50 + "\n\n")

            for profile, password in wifi_data:
                f.write(f"Network: {profile}\n")
                f.write(f"Password: {password}\n")
                f.write("-" * 30 + "\n")

        print(f"WiFi information saved to {filename}")

    except Exception as e:
        print(f"Error saving to file: {e}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nOperation cancelled by user.")
    except Exception as e:
        print(f"An error occurred: {e}")