#!/usr/bin/env python3
"""
WiFi Password Retriever and Speed Tester
This script retrieves saved WiFi passwords and tests current WiFi speed on Windows systems.
"""

import subprocess
import re
import sys
import os
import time
import requests
import threading
from datetime import datetime

def get_current_wifi_info():
    """Get information about the currently connected WiFi network."""
    try:
        # Get current WiFi connection info
        result = subprocess.run(
            ['netsh', 'wlan', 'show', 'interfaces'],
            capture_output=True,
            text=True,
            check=True
        )

        wifi_info = {}
        for line in result.stdout.split('\n'):
            line = line.strip()
            if 'SSID' in line and 'BSSID' not in line:
                ssid_match = re.search(r':\s*(.+)', line)
                if ssid_match:
                    wifi_info['ssid'] = ssid_match.group(1).strip()
            elif 'Signal' in line:
                signal_match = re.search(r':\s*(.+)', line)
                if signal_match:
                    wifi_info['signal'] = signal_match.group(1).strip()
            elif 'Radio type' in line:
                radio_match = re.search(r':\s*(.+)', line)
                if radio_match:
                    wifi_info['radio_type'] = radio_match.group(1).strip()
            elif 'Receive rate' in line:
                rx_match = re.search(r':\s*(.+)', line)
                if rx_match:
                    wifi_info['receive_rate'] = rx_match.group(1).strip()
            elif 'Transmit rate' in line:
                tx_match = re.search(r':\s*(.+)', line)
                if tx_match:
                    wifi_info['transmit_rate'] = tx_match.group(1).strip()

        return wifi_info

    except subprocess.CalledProcessError as e:
        print(f"Error getting current WiFi info: {e}")
        return {}
    except Exception as e:
        print(f"Unexpected error: {e}")
        return {}

def get_wifi_profiles():
    """Get all WiFi profiles saved on the system."""
    try:
        # Run netsh command to get WiFi profiles
        result = subprocess.run(
            ['netsh', 'wlan', 'show', 'profiles'],
            capture_output=True,
            text=True,
            check=True
        )

        # Extract profile names using regex
        profiles = []
        for line in result.stdout.split('\n'):
            if 'All User Profile' in line:
                # Extract profile name
                profile_match = re.search(r':\s*(.+)', line)
                if profile_match:
                    profile_name = profile_match.group(1).strip()
                    profiles.append(profile_name)

        return profiles

    except subprocess.CalledProcessError as e:
        print(f"Error getting WiFi profiles: {e}")
        return []
    except Exception as e:
        print(f"Unexpected error: {e}")
        return []

def test_download_speed(url, timeout=10):
    """Test download speed by downloading a file."""
    try:
        start_time = time.time()
        response = requests.get(url, timeout=timeout, stream=True)

        if response.status_code == 200:
            total_size = 0
            for chunk in response.iter_content(chunk_size=1024):
                total_size += len(chunk)

            end_time = time.time()
            duration = end_time - start_time

            # Convert to Mbps
            speed_mbps = (total_size * 8) / (duration * 1000000)
            return speed_mbps, total_size, duration
        else:
            return None, 0, 0

    except Exception as e:
        print(f"Download test error: {e}")
        return None, 0, 0

def test_upload_speed(url, data_size_mb=1, timeout=10):
    """Test upload speed by uploading data."""
    try:
        # Create dummy data
        data = b'0' * (data_size_mb * 1024 * 1024)

        start_time = time.time()
        response = requests.post(url, data=data, timeout=timeout)
        end_time = time.time()

        if response.status_code in [200, 201]:
            duration = end_time - start_time
            speed_mbps = (len(data) * 8) / (duration * 1000000)
            return speed_mbps, len(data), duration
        else:
            return None, 0, 0

    except Exception as e:
        print(f"Upload test error: {e}")
        return None, 0, 0

def test_ping(host="*******"):
    """Test ping to a host."""
    try:
        result = subprocess.run(
            ['ping', '-n', '4', host],
            capture_output=True,
            text=True,
            check=True
        )

        # Extract average ping time
        for line in result.stdout.split('\n'):
            if 'Average' in line:
                ping_match = re.search(r'(\d+)ms', line)
                if ping_match:
                    return int(ping_match.group(1))

        return None

    except Exception as e:
        print(f"Ping test error: {e}")
        return None

def run_speed_test():
    """Run comprehensive WiFi speed test."""
    print("Running WiFi Speed Test...")
    print("=" * 50)

    # Test URLs (using reliable speed test servers)
    download_urls = [
        "http://speedtest.ftp.otenet.gr/files/test1Mb.db",  # 1MB file
        "http://speedtest.ftp.otenet.gr/files/test10Mb.db", # 10MB file
    ]

    # Test ping first
    print("Testing ping...")
    ping_time = test_ping()
    if ping_time:
        print(f"Ping: {ping_time} ms")
    else:
        print("Ping: Failed")

    print("\nTesting download speed...")
    download_speeds = []

    for i, url in enumerate(download_urls):
        print(f"Test {i+1}/2: Downloading test file...")
        speed, size, duration = test_download_speed(url)
        if speed:
            download_speeds.append(speed)
            print(f"Speed: {speed:.2f} Mbps ({size/1024/1024:.1f} MB in {duration:.1f}s)")
        else:
            print("Download test failed")

    if download_speeds:
        avg_download = sum(download_speeds) / len(download_speeds)
        print(f"\nAverage Download Speed: {avg_download:.2f} Mbps")
    else:
        print("\nDownload speed test failed")

    return {
        'ping': ping_time,
        'download_speeds': download_speeds,
        'avg_download': avg_download if download_speeds else 0
    }

def get_wifi_password(profile_name):
    """Get the password for a specific WiFi profile."""
    try:
        # Run netsh command to get profile details with key
        result = subprocess.run(
            ['netsh', 'wlan', 'show', 'profile', profile_name, 'key=clear'],
            capture_output=True,
            text=True,
            check=True
        )

        # Look for the key content (password)
        for line in result.stdout.split('\n'):
            if 'Key Content' in line:
                password_match = re.search(r':\s*(.+)', line)
                if password_match:
                    return password_match.group(1).strip()

        return "No password found or open network"

    except subprocess.CalledProcessError:
        return "Access denied or profile not found"
    except Exception as e:
        return f"Error: {e}"

def main():
    """Main function to retrieve WiFi info, passwords, and test speed."""
    print("WiFi Password Retriever and Speed Tester")
    print("=" * 60)

    # Check if running on Windows
    if os.name != 'nt':
        print("This script is designed for Windows systems only.")
        sys.exit(1)

    # Show menu options
    print("\nSelect an option:")
    print("1. Show current WiFi information and test speed")
    print("2. Show all saved WiFi passwords")
    print("3. Both options")

    choice = input("\nEnter your choice (1-3): ").strip()

    if choice in ['1', '3']:
        # Get current WiFi info
        print("\n" + "="*60)
        print("CURRENT WiFi CONNECTION INFO")
        print("="*60)

        current_wifi = get_current_wifi_info()
        if current_wifi:
            print(f"Connected Network: {current_wifi.get('ssid', 'Unknown')}")
            print(f"Signal Strength: {current_wifi.get('signal', 'Unknown')}")
            print(f"Radio Type: {current_wifi.get('radio_type', 'Unknown')}")
            print(f"Receive Rate: {current_wifi.get('receive_rate', 'Unknown')}")
            print(f"Transmit Rate: {current_wifi.get('transmit_rate', 'Unknown')}")

            # Run speed test
            print(f"\nTesting speed for: {current_wifi.get('ssid', 'Current Network')}")
            speed_results = run_speed_test()

            # Display speed test summary
            print("\n" + "="*60)
            print("SPEED TEST SUMMARY")
            print("="*60)
            print(f"Network: {current_wifi.get('ssid', 'Unknown')}")
            print(f"Ping: {speed_results['ping']} ms" if speed_results['ping'] else "Ping: Failed")
            if speed_results['avg_download'] > 0:
                print(f"Average Download Speed: {speed_results['avg_download']:.2f} Mbps")

                # Speed classification
                if speed_results['avg_download'] >= 100:
                    speed_class = "Excellent (100+ Mbps)"
                elif speed_results['avg_download'] >= 25:
                    speed_class = "Good (25-100 Mbps)"
                elif speed_results['avg_download'] >= 10:
                    speed_class = "Fair (10-25 Mbps)"
                else:
                    speed_class = "Slow (<10 Mbps)"

                print(f"Speed Classification: {speed_class}")
            else:
                print("Download Speed: Test Failed")
        else:
            print("No WiFi connection found or unable to retrieve information.")

    if choice in ['2', '3']:
        # Get all WiFi profiles and passwords
        print("\n" + "="*60)
        print("SAVED WiFi PASSWORDS")
        print("="*60)

        print("Retrieving WiFi profiles...")
        profiles = get_wifi_profiles()

        if not profiles:
            print("No WiFi profiles found.")
        else:
            print(f"Found {len(profiles)} WiFi profile(s):\n")

            # Get password for each profile
            wifi_data = []
            for profile in profiles:
                password = get_wifi_password(profile)
                wifi_data.append((profile, password))

            # Display results
            for profile, password in wifi_data:
                print(f"Network: {profile}")
                print(f"Password: {password}")
                print("-" * 40)

            # Option to save to file
            save_choice = input("\nDo you want to save this information to a file? (y/n): ").lower()
            if save_choice == 'y':
                save_to_file(wifi_data, current_wifi if choice == '3' else None,
                           speed_results if choice == '3' else None)

def save_to_file(wifi_data, current_wifi=None, speed_results=None):
    """Save WiFi data to a text file."""
    try:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"wifi_report_{timestamp}.txt"

        with open(filename, 'w', encoding='utf-8') as f:
            f.write("WiFi Analysis Report\n")
            f.write("=" * 60 + "\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            # Current WiFi info and speed test
            if current_wifi and speed_results:
                f.write("CURRENT WiFi CONNECTION\n")
                f.write("-" * 30 + "\n")
                f.write(f"Network: {current_wifi.get('ssid', 'Unknown')}\n")
                f.write(f"Signal Strength: {current_wifi.get('signal', 'Unknown')}\n")
                f.write(f"Radio Type: {current_wifi.get('radio_type', 'Unknown')}\n")
                f.write(f"Receive Rate: {current_wifi.get('receive_rate', 'Unknown')}\n")
                f.write(f"Transmit Rate: {current_wifi.get('transmit_rate', 'Unknown')}\n")

                f.write(f"\nSPEED TEST RESULTS\n")
                f.write("-" * 30 + "\n")
                f.write(f"Ping: {speed_results['ping']} ms\n" if speed_results['ping'] else "Ping: Failed\n")
                if speed_results['avg_download'] > 0:
                    f.write(f"Average Download Speed: {speed_results['avg_download']:.2f} Mbps\n")
                else:
                    f.write("Download Speed: Test Failed\n")
                f.write("\n")

            # Saved passwords
            if wifi_data:
                f.write("SAVED WiFi PASSWORDS\n")
                f.write("-" * 30 + "\n")
                for profile, password in wifi_data:
                    f.write(f"Network: {profile}\n")
                    f.write(f"Password: {password}\n")
                    f.write("-" * 20 + "\n")

        print(f"WiFi report saved to {filename}")

    except Exception as e:
        print(f"Error saving to file: {e}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nOperation cancelled by user.")
    except Exception as e:
        print(f"An error occurred: {e}")